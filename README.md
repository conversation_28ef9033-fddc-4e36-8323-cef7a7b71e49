[TOC]

# emailAIReader

## 项目描述
+ 主要用于在获取用户邮件授权后，能够读取用户的邮件内容和附件信息
+ 对于邮件内容能够使用AI大模型进行解读整理安排合理的工作项
+ 对于excel、pdf、word、图片等附件，能够合理的创建文件夹进行整理（有默认整理规则，用户可自定义）
  + 默认整理规则：年月日+附件名
  + 如果设置上传路径，在确定有权上传后能自动上传（同样使用规则整理远程上传附件）
## 技术架构
+ 使用springBoot + vue
+ springAIalibaba
+ n8n工作流

## 工作流地址
+ http://10.154.177.213:5678/workflow/4dU4IflzJ0TP4Fka 
+ 账户 <EMAIL> 
+ 密码 f9t3ZFuYsCRbJNQ

## 数据库
+ http://10.154.177.213:5433/email_ai_reader

+ email_ai_reader
+ 4DwcB8Q2ncSd8pYh
