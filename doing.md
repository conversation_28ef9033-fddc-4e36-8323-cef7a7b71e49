好的，我看到你提供了数据库连接信息。让我更新配置文件以使用你的PostgreSQL数据库：

````yaml path=src/main/resources/application.yml mode=EDIT
spring:
  application:
    name: email-ai-reader
  
  datasource:
    url: *****************************************************
    username: email_ai_reader
    password: 4DwcB8Q2ncSd8pYh
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    
  mail:
    properties:
      "[mail.smtp.connectiontimeout]": 5000
      "[mail.smtp.timeout]": 3000
      "[mail.smtp.writetimeout]": 5000

  ai:
    alibaba:
      api-key: ${ALIBABA_AI_API_KEY}
      model: qwen-plus

server:
  port: 8080

app:
  file:
    upload-dir: ${FILE_UPLOAD_DIR:./uploads}
    max-size: 50MB
  email:
    attachment-dir: ${ATTACHMENT_DIR:./attachments}
````

同时需要更新`pom.xml`中的数据库依赖：

````xml path=pom.xml mode=EDIT
        <!-- Database -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
````

现在数据库配置已经更新为使用你的PostgreSQL数据库。接下来我们可以创建数据库表结构的SQL脚本：

````sql path=src/main/resources/db/migration/V1__Create_initial_tables.sql mode=EDIT
-- 邮箱账户表
CREATE TABLE email_accounts (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    encrypted_password VARCHAR(500) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    imap_server VARCHAR(255),
    imap_port INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sync TIMESTAMP
);

-- 邮件消息表
CREATE TABLE email_messages (
    id BIGSERIAL PRIMARY KEY,
    account_id BIGINT REFERENCES email_accounts(id),
    subject VARCHAR(500),
    sender VARCHAR(255),
    recipient VARCHAR(255),
    content TEXT,
    ai_summary TEXT,
    received_at TIMESTAMP,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 邮件附件表
CREATE TABLE email_attachments (
    id BIGSERIAL PRIMARY KEY,
    email_message_id BIGINT REFERENCES email_messages(id),
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100),
    file_size BIGINT,
    file_path VARCHAR(500),
    remote_path VARCHAR(500),
    ai_category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文件整理规则表
CREATE TABLE file_organization_rules (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    rule_name VARCHAR(100) NOT NULL,
    file_pattern VARCHAR(255),
    target_path_template VARCHAR(500),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_email_messages_account_id ON email_messages(account_id);
CREATE INDEX idx_email_attachments_message_id ON email_attachments(email_message_id);
CREATE INDEX idx_email_messages_received_at ON email_messages(received_at);
````

数据库配置完成！现在你的应用将连接到PostgreSQL数据库。启动应用时，Hibernate会自动创建表结构。


